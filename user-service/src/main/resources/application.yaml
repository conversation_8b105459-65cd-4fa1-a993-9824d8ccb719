server:
    port: 8083
spring:
    application:
        name: user-service
    profiles:
        active: dev
    datasource:
        url: jdbc:mysql://${hm.db.host}:3306/hm-user?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&serverTimezone=Asia/Shanghai
        driver-class-name: com.mysql.cj.jdbc.Driver
        username: root
        password: ${hm.db.pw}
    cloud:
        nacos:
            server-addr: localhost:8848 # nacos地址
mybatis-plus:
    configuration:
        default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
    global-config:
        db-config:
            update-strategy: not-null
            id-type: auto
logging:
    level:
        com.hmall: debug
    pattern:
        dateformat: HH:mm:ss:SSS
    file:
        path: "logs/${spring.application.name}"
knife4j:
    enable: true
    openapi:
        title: 黑马商城用户服务接口文档
        description: "黑马商城用户服务接口文档"
        email: z<PERSON><PERSON><EMAIL>
        concat: 虎哥
        url: https://www.itcast.cn
        version: v1.0.0
        group:
            default:
                group-name: default
                api-rule: package
                api-rule-resources:
                    - com.hmall.user.controller
hm:
    jwt:
        location: classpath:hmall.jks
        alias: hmall
        password: hmall123
        tokenTTL: 30m
# keytool -genkeypair -alias hmall -keyalg RSA -keypass hmall123 -keystore hmall.jks -storepass hmall123
